import SwiftUI
import WebKit

// MARK: - Browser View Model
class BrowserViewModel: ObservableObject {
    @Published var currentURL: String = "about:blank"
    @Published var canGoBack: Bool = false
    @Published var canGoForward: Bool = false
    @Published var isLoading: Bool = false
    @Published var pageTitle: String = ""
    @Published var loadingProgress: Double = 0.0

    weak var webView: WKWebView?

    // 默认搜索引擎
    private let defaultSearchEngine = "https://www.google.com/search?q="

    func loadURL(_ urlString: String) {
        print("🔍 loadURL被调用，输入: '\(urlString)'")
        let processedURL = processURLInput(urlString)
        print("🔍 处理后的URL: '\(processedURL)'")

        guard let url = URL(string: processedURL) else {
            print("❌ 无效的URL: \(urlString) -> \(processedURL)")
            return
        }

        print("🔍 WebView状态: \(webView != nil ? "存在" : "不存在")")

        // 不要立即更新currentURL，让WebView加载完成后再更新
        webView?.load(URLRequest(url: url))
        print("🌐 开始加载URL: \(processedURL)")
    }

    // 智能处理URL输入
    private func processURLInput(_ input: String) -> String {
        let trimmedInput = input.trimmingCharacters(in: .whitespacesAndNewlines)

        // 如果为空，返回Google首页
        if trimmedInput.isEmpty {
            return "https://www.google.com"
        }

        // 如果已经包含协议，直接返回
        if trimmedInput.hasPrefix("http://") || trimmedInput.hasPrefix("https://") {
            return trimmedInput
        }

        // 如果看起来像域名（包含点且不包含空格）
        if trimmedInput.contains(".") && !trimmedInput.contains(" ") {
            return "https://" + trimmedInput
        }

        // 否则作为搜索词处理
        let encodedQuery = trimmedInput.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? trimmedInput
        return defaultSearchEngine + encodedQuery
    }

    func goBack() {
        webView?.goBack()
    }

    func goForward() {
        webView?.goForward()
    }

    func reload() {
        webView?.reload()
    }

    func stopLoading() {
        webView?.stopLoading()
    }

    func updateNavigationState() {
        canGoBack = webView?.canGoBack ?? false
        canGoForward = webView?.canGoForward ?? false
        isLoading = webView?.isLoading ?? false
        pageTitle = webView?.title ?? ""
        loadingProgress = webView?.estimatedProgress ?? 0.0
    }
}

// MARK: - WebView Wrapper
struct WebView: UIViewRepresentable {
    @ObservedObject var browserViewModel: BrowserViewModel

    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView()
        webView.navigationDelegate = context.coordinator
        browserViewModel.webView = webView

        // 添加KVO观察器来跟踪加载进度
        webView.addObserver(context.coordinator, forKeyPath: #keyPath(WKWebView.estimatedProgress), options: .new, context: nil)
        webView.addObserver(context.coordinator, forKeyPath: #keyPath(WKWebView.title), options: .new, context: nil)

        // 不要自动加载about:blank，让它保持空白状态
        print("🔍 WebView已创建，初始URL: \(browserViewModel.currentURL)")

        return webView
    }

    func updateUIView(_ webView: WKWebView, context: Context) {
        // Update if needed
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, WKNavigationDelegate {
        let parent: WebView

        init(_ parent: WebView) {
            self.parent = parent
            super.init()
        }

        deinit {
            // 移除KVO观察器
            parent.browserViewModel.webView?.removeObserver(self, forKeyPath: #keyPath(WKWebView.estimatedProgress))
            parent.browserViewModel.webView?.removeObserver(self, forKeyPath: #keyPath(WKWebView.title))
        }

        // MARK: - KVO Observer
        override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
            if keyPath == #keyPath(WKWebView.estimatedProgress) {
                DispatchQueue.main.async {
                    self.parent.browserViewModel.loadingProgress = self.parent.browserViewModel.webView?.estimatedProgress ?? 0.0
                }
            } else if keyPath == #keyPath(WKWebView.title) {
                DispatchQueue.main.async {
                    self.parent.browserViewModel.pageTitle = self.parent.browserViewModel.webView?.title ?? ""
                }
            }
        }

        // MARK: - WKNavigationDelegate
        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            DispatchQueue.main.async {
                self.parent.browserViewModel.isLoading = true
                self.parent.browserViewModel.updateNavigationState()
            }
            print("🌐 开始加载网页...")
        }

        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            DispatchQueue.main.async {
                self.parent.browserViewModel.isLoading = false
                self.parent.browserViewModel.updateNavigationState()
                if let url = webView.url {
                    self.parent.browserViewModel.currentURL = url.absoluteString
                }
            }
            print("✅ 网页加载完成: \(webView.url?.absoluteString ?? "未知")")
        }

        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            DispatchQueue.main.async {
                self.parent.browserViewModel.isLoading = false
                self.parent.browserViewModel.updateNavigationState()
            }
            print("❌ 网页加载失败: \(error.localizedDescription)")
        }

        func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
            DispatchQueue.main.async {
                self.parent.browserViewModel.isLoading = false
                self.parent.browserViewModel.updateNavigationState()
            }
            print("❌ 网页预加载失败: \(error.localizedDescription)")
        }

        func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
            print("🔗 导航到: \(navigationAction.request.url?.absoluteString ?? "未知")")
            decisionHandler(.allow)
        }
    }
}

// MARK: - Address Bar Component
struct AddressBar: View {
    @ObservedObject var browserViewModel: BrowserViewModel
    @State private var editingURL: String = ""
    @State private var isEditing: Bool = false
    @FocusState private var isTextFieldFocused: Bool

    var body: some View {
        VStack(spacing: 0) {
            HStack(spacing: 12) {
                // Back button
                Button(action: { browserViewModel.goBack() }) {
                    Image(systemName: "chevron.left")
                        .font(.title2)
                        .foregroundColor(browserViewModel.canGoBack ? .blue : .gray)
                }
                .disabled(!browserViewModel.canGoBack)

                // Forward button
                Button(action: { browserViewModel.goForward() }) {
                    Image(systemName: "chevron.right")
                        .font(.title2)
                        .foregroundColor(browserViewModel.canGoForward ? .blue : .gray)
                }
                .disabled(!browserViewModel.canGoForward)

                // Reload/Stop button
                Button(action: {
                    if browserViewModel.isLoading {
                        browserViewModel.stopLoading()
                    } else {
                        browserViewModel.reload()
                    }
                }) {
                    Image(systemName: browserViewModel.isLoading ? "xmark" : "arrow.clockwise")
                        .font(.title2)
                        .foregroundColor(.blue)
                }

                // URL TextField with enhanced styling
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)
                        .font(.system(size: 16))

                    TextField("输入网址或搜索", text: isEditing ? $editingURL : .constant(simplifiedCurrentURL))
                        .focused($isTextFieldFocused)
                        .textInputAutocapitalization(.never)
                        .autocorrectionDisabled()
                        .keyboardType(.webSearch)
                        .onTapGesture {
                            startEditing()
                        }
                        .onSubmit {
                            submitURL()
                        }
                        .onChange(of: browserViewModel.currentURL) { _, newValue in
                            if !isEditing && newValue != "about:blank" {
                                editingURL = newValue
                            }
                        }

                    if isEditing {
                        Button("取消") {
                            cancelEditing()
                        }
                        .foregroundColor(.blue)
                        .font(.system(size: 16))
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color(.systemGray5))
                .cornerRadius(8)
            }
            .padding(.horizontal)
            .padding(.vertical, 8)

            // Progress bar
            if browserViewModel.isLoading {
                ProgressView(value: browserViewModel.loadingProgress)
                    .progressViewStyle(LinearProgressViewStyle())
                    .animation(.easeInOut(duration: 0.2), value: browserViewModel.loadingProgress)
            }
        }
        .background(Color(.systemGray6))
    }

    // 计算简化的当前URL（用于非编辑状态显示）
    private var simplifiedCurrentURL: String {
        let url = browserViewModel.currentURL
        // 如果是空白页，返回空字符串
        if url == "about:blank" {
            return ""
        }
        // 移除协议前缀以简化显示
        if url.hasPrefix("https://") {
            return String(url.dropFirst(8))
        } else if url.hasPrefix("http://") {
            return String(url.dropFirst(7))
        }
        return url
    }

    // 计算显示的URL（简化显示）
    private var displayURL: String {
        if isEditing {
            return editingURL
        }
        return simplifiedCurrentURL
    }

    private func startEditing() {
        isEditing = true
        // 开始编辑时清空输入框，让用户可以输入新的URL
        editingURL = ""
        isTextFieldFocused = true
    }

    private func submitURL() {
        let urlToLoad = editingURL
        print("📝 submitURL被调用，输入: '\(urlToLoad)'")
        isEditing = false
        isTextFieldFocused = false
        browserViewModel.loadURL(urlToLoad)
        print("📝 已调用browserViewModel.loadURL")
    }

    private func cancelEditing() {
        isEditing = false
        isTextFieldFocused = false
        editingURL = browserViewModel.currentURL
    }
}

// MARK: - Modern External Screen Manager for Browser Display (iOS 16+ Optimized)
@available(iOS 16.0, *)
class ExternalScreenManager: ObservableObject {
    @Published var hasExternalScreen = false
    @Published var externalScreenContent: ExternalScreenContent = .browser
    @Published var screenAspectRatio: CGFloat = 16.0/9.0

    private var externalWindow: UIWindow?
    private var externalViewController: UIViewController?
    private var externalWindowScene: UIWindowScene?

    enum ExternalScreenContent {
        case browser        // 浏览器显示内容
        case custom         // 自定义内容
    }

    private var browserViewModel: BrowserViewModel?

    init() {
        setupModernScreenDetection()
    }

    // 使用现代的场景检测方法 (iOS 16+)
    private func setupModernScreenDetection() {
        checkForExternalScreenModern()

        // 监听场景变化 (替代弃用的UIScreen通知)
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(sceneDidActivate),
            name: UIScene.didActivateNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(sceneDidDisconnect),
            name: UIScene.didDisconnectNotification,
            object: nil
        )
    }

    @objc private func sceneDidActivate(_ notification: Notification) {
        DispatchQueue.main.async {
            self.checkForExternalScreenModern()
        }
    }

    @objc private func sceneDidDisconnect(_ notification: Notification) {
        DispatchQueue.main.async {
            self.checkForExternalScreenModern()
            if !self.hasExternalScreen {
                self.cleanupExternalDisplay()
            }
        }
    }

    // 现代化的外接屏幕检测 (使用UIApplication.shared.connectedScenes)
    private func checkForExternalScreenModern() {
        let connectedScenes = UIApplication.shared.connectedScenes
        let windowScenes = connectedScenes.compactMap { $0 as? UIWindowScene }

        // 查找非主屏幕的场景
        let externalScenes = windowScenes.filter { scene in
            scene.screen != UIScreen.main
        }

        let newHasExternalScreen = !externalScenes.isEmpty

        if newHasExternalScreen, let externalScene = externalScenes.first {
            let externalScreen = externalScene.screen
            let bounds = externalScreen.bounds
            screenAspectRatio = bounds.width / bounds.height
            externalWindowScene = externalScene

            // 检查是否为宽屏 (宽高比大于1.5)
            let isWideScreen = screenAspectRatio > 1.5
            print("✅ 外接屏幕检测: \(bounds), 宽高比: \(String(format: "%.2f", screenAspectRatio)), 宽屏: \(isWideScreen)")

            // 默认设置为浏览器显示
            externalScreenContent = .browser
        } else {
            externalWindowScene = nil
        }

        hasExternalScreen = newHasExternalScreen
    }

    private func cleanupExternalDisplay() {
        externalWindow?.isHidden = true
        externalWindow = nil
        externalViewController = nil
        externalWindowScene = nil
        print("🗑️ 外接屏幕显示已清理")
    }

    // 现代化的外接屏幕浏览器设置方法
    func setupExternalBrowserDisplay(with browserViewModel: BrowserViewModel) {
        guard hasExternalScreen, let windowScene = externalWindowScene else {
            print("❌ 没有检测到外接屏幕或WindowScene")
            return
        }

        self.browserViewModel = browserViewModel
        let externalScreen = windowScene.screen

        print("🖥️ 开始设置外接屏幕浏览器显示...")
        print("📱 主屏幕: \(UIScreen.main.bounds)")
        print("🖥️ 外接屏幕: \(externalScreen.bounds)")
        print("  镜像状态: \(externalScreen.mirrored != nil ? "镜像模式" : "扩展模式")")

        // 清理之前的窗口
        cleanupExternalDisplay()

        // 使用WindowScene创建窗口 (现代化方法，避免弃用警告)
        let newWindow = UIWindow(windowScene: windowScene)
        newWindow.windowLevel = UIWindow.Level.normal
        newWindow.backgroundColor = UIColor.black

        // 创建浏览器内容视图控制器
        let contentVC = ModernExternalBrowserViewController()
        contentVC.setupForScreen(externalScreen, browserViewModel: browserViewModel)

        newWindow.rootViewController = contentVC
        newWindow.isHidden = false
        newWindow.makeKeyAndVisible()

        // 保存引用
        externalWindow = newWindow
        externalViewController = contentVC

        print("✅ 现代化外接屏幕浏览器窗口已创建")

        // 恢复主屏幕焦点
        restoreMainWindowFocus()
    }

    // 恢复主屏幕焦点的现代化方法
    private func restoreMainWindowFocus() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            let connectedScenes = UIApplication.shared.connectedScenes
            let mainWindowScene = connectedScenes.first { scene in
                (scene as? UIWindowScene)?.screen == UIScreen.main
            } as? UIWindowScene

            if let mainScene = mainWindowScene,
               let mainWindow = mainScene.windows.first(where: { $0.isKeyWindow == false }) {
                mainWindow.makeKeyAndVisible()
                print("🏠 主屏幕焦点已恢复")
            }
        }
    }



    func updateExternalContent(_ content: ExternalScreenContent) {
        externalScreenContent = content
        print("External screen content changed to: \(content)")

        // 强制重新设置外接屏幕以确保独立显示
        if hasExternalScreen, let browserVM = browserViewModel {
            setupExternalBrowserDisplay(with: browserVM)
        }
    }

    func setBrowserViewModel(_ browserVM: BrowserViewModel) {
        browserViewModel = browserVM
    }

    // 强制刷新外接屏幕显示
    func forceRefreshExternalDisplay() {
        guard hasExternalScreen, let browserVM = browserViewModel else {
            print("❌ 无法刷新外接屏幕")
            return
        }

        print("🔄 强制刷新外接屏幕显示...")
        cleanupExternalDisplay()
        setupExternalBrowserDisplay(with: browserVM)
    }













    // 现代化的WindowScene扩展屏幕方法（增强诊断）
    func createExtendedScreenWithWindowScene() {
        print("🎭 使用现代化WindowScene创建扩展屏幕...")

        // 详细诊断
        print("🔍 详细诊断开始:")
        print("   hasExternalScreen: \(hasExternalScreen)")
        print("   externalWindowScene: \(externalWindowScene != nil)")

        // 检查连接的场景
        let connectedScenes = UIApplication.shared.connectedScenes
        print("   连接的场景数量: \(connectedScenes.count)")

        for (index, scene) in connectedScenes.enumerated() {
            if let windowScene = scene as? UIWindowScene {
                print("   场景\(index): \(windowScene.screen.bounds), 主屏幕: \(windowScene.screen == UIScreen.main)")
            }
        }

        // 重新检查外接屏幕
        checkForExternalScreenModern()

        guard hasExternalScreen else {
            print("❌ 没有检测到外接屏幕")
            print("💡 请确保:")
            print("   1. 外接显示器已连接并开启")
            print("   2. iOS设备识别了外接显示器")
            print("   3. 在iOS控制中心设置为扩展桌面模式")
            showExtendedDesktopInstructions()
            return
        }

        guard let windowScene = externalWindowScene else {
            print("❌ 没有找到外接屏幕的WindowScene")
            print("💡 这通常意味着外接屏幕处于镜像模式")
            print("📋 请在iOS控制中心设置为扩展桌面模式")
            showExtendedDesktopInstructions()
            return
        }

        let externalScreen = windowScene.screen
        print("✅ 找到外接屏幕WindowScene")
        print("🖥️ 目标外接屏幕: \(externalScreen.bounds)")
        print("🔄 镜像状态: \(externalScreen.mirrored != nil ? "镜像模式" : "扩展模式")")

        if externalScreen.mirrored != nil {
            print("⚠️ 外接屏幕仍处于镜像模式!")
            print("📋 请按照以下步骤设置扩展桌面:")
            showExtendedDesktopInstructions()
            // 仍然尝试创建窗口，但会显示镜像警告
        }

        // 清理旧窗口
        cleanupExternalDisplay()

        // 使用现代化WindowScene方法创建窗口
        createModernWindowSceneDisplay(windowScene: windowScene)
    }

    // 强制设置扩展桌面模式的指导
    func showExtendedDesktopInstructions() {
        print("📋 设置扩展桌面模式的详细步骤:")
        print("🎯 目标：将外接屏幕从镜像模式切换到扩展桌面模式")
        print("📱 iPad/iPhone操作步骤:")
        print("   1. 确保外接屏幕已连接并开启")
        print("   2. 从iPad右上角向下滑动，打开控制中心")
        print("   3. 找到'屏幕镜像'图标（📺）")
        print("   4. 长按'屏幕镜像'图标（不是轻点）")
        print("   5. 在弹出的菜单中选择你的外接显示器")
        print("   6. 选择'扩展桌面'选项（而不是'镜像'）")
    }

    // 现代化的WindowScene显示创建（增强诊断）
    private func createModernWindowSceneDisplay(windowScene: UIWindowScene) {
        print("🪟 使用现代化WindowScene创建扩展显示...")

        let externalScreen = windowScene.screen
        print("📺 WindowScene屏幕信息:")
        print("   尺寸: \(externalScreen.bounds)")
        print("   缩放: \(externalScreen.scale)")
        print("   镜像: \(externalScreen.mirrored != nil)")

        // 使用WindowScene创建窗口 (避免弃用警告)
        let window = UIWindow(windowScene: windowScene)
        window.backgroundColor = UIColor.black
        window.windowLevel = UIWindow.Level.normal

        print("🔧 窗口创建信息:")
        print("   窗口场景: \(window.windowScene != nil)")
        print("   窗口尺寸: \(window.frame)")

        // 创建现代化的浏览器内容视图控制器
        let contentVC = ModernExternalBrowserViewController()
        if let browserVM = browserViewModel {
            contentVC.setupForScreen(externalScreen, browserViewModel: browserVM)
        } else {
            // 如果没有浏览器视图模型，创建一个临时的
            let tempBrowserVM = BrowserViewModel()
            contentVC.setupForScreen(externalScreen, browserViewModel: tempBrowserVM)
        }

        window.rootViewController = contentVC

        print("🎬 设置窗口显示...")
        window.isHidden = false
        window.makeKeyAndVisible()

        // 验证窗口状态
        print("✅ 窗口状态验证:")
        print("   窗口可见: \(!window.isHidden)")
        print("   窗口主键: \(window.isKeyWindow)")
        print("   根视图控制器: \(window.rootViewController != nil)")

        // 保存引用
        externalWindow = window
        externalViewController = contentVC

        print("✅ 现代化WindowScene扩展显示已创建")

        // 延迟验证
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            print("🔍 延迟验证:")
            print("   窗口仍然可见: \(!window.isHidden)")
            print("   窗口仍然主键: \(window.isKeyWindow)")
            print("   视图控制器已加载: \(contentVC.isViewLoaded)")
        }

        // 恢复主屏幕焦点
        restoreMainWindowFocus()
    }

    // 简单的外接屏幕测试方法
    func testBasicExternalScreen() {
        print("🧪 基础外接屏幕测试...")

        // 检查连接的场景
        let connectedScenes = UIApplication.shared.connectedScenes
        print("📊 场景统计:")
        print("   总场景数: \(connectedScenes.count)")

        let windowScenes = connectedScenes.compactMap { $0 as? UIWindowScene }
        print("   窗口场景数: \(windowScenes.count)")

        for (index, scene) in windowScenes.enumerated() {
            let screen = scene.screen
            let isMain = screen == UIScreen.main
            let isMirrored = screen.mirrored != nil

            print("   场景\(index):")
            print("     屏幕尺寸: \(screen.bounds)")
            print("     是主屏幕: \(isMain)")
            print("     镜像状态: \(isMirrored ? "镜像" : "独立")")
            print("     场景状态: \(scene.activationState.rawValue)")
        }

        // 查找外接屏幕场景
        let externalScenes = windowScenes.filter { $0.screen != UIScreen.main }

        if externalScenes.isEmpty {
            print("❌ 没有找到外接屏幕场景")
            print("💡 可能的原因:")
            print("   1. 外接屏幕未连接")
            print("   2. 外接屏幕处于镜像模式")
            print("   3. iOS未创建独立的WindowScene")
            return
        }

        print("✅ 找到 \(externalScenes.count) 个外接屏幕场景")

        // 尝试在第一个外接屏幕场景创建简单窗口
        let externalScene = externalScenes[0]
        createSimpleTestWindow(in: externalScene)
    }

    private func createSimpleTestWindow(in windowScene: UIWindowScene) {
        print("🪟 在外接屏幕场景创建简单测试窗口...")

        // 清理旧窗口
        cleanupExternalDisplay()

        // 创建简单的测试窗口
        let testWindow = UIWindow(windowScene: windowScene)
        testWindow.backgroundColor = UIColor.systemRed

        // 创建简单的测试视图控制器
        let testVC = UIViewController()
        testVC.view.backgroundColor = UIColor.systemBlue

        // 添加简单的浏览器标签
        let label = UILabel()
        label.text = "🌐 外接屏幕浏览器\n\n如果你看到这个界面\n说明外接屏幕连接成功!\n\n准备显示网页内容"
        label.textColor = UIColor.white
        label.font = UIFont.boldSystemFont(ofSize: 32)
        label.textAlignment = .center
        label.numberOfLines = 0
        label.frame = testVC.view.bounds
        label.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        testVC.view.addSubview(label)

        testWindow.rootViewController = testVC
        testWindow.makeKeyAndVisible()

        // 保存引用
        externalWindow = testWindow
        externalViewController = testVC

        print("✅ 简单浏览器测试窗口已创建")
        print("👀 请查看外接屏幕是否显示浏览器测试界面")

        // 恢复主屏幕焦点
        restoreMainWindowFocus()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Modern External Display View Controller
@available(iOS 16.0, *)
class ModernExternalDisplayViewController: UIViewController {
    private var screenInfo: UIScreen?
    private var contentType: ExternalScreenManager.ExternalScreenContent = .browser

    func setupForScreen(_ screen: UIScreen, content: ExternalScreenManager.ExternalScreenContent) {
        self.screenInfo = screen
        self.contentType = content
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupModernInterface()
    }

    private func setupModernInterface() {
        guard let screen = screenInfo else { return }

        // 设置现代化渐变背景
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = view.bounds
        gradientLayer.colors = [
            UIColor.systemGreen.cgColor,
            UIColor.systemBlue.cgColor,
            UIColor.systemPurple.cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        view.layer.insertSublayer(gradientLayer, at: 0)

        // 创建主容器
        let containerView = UIView()
        containerView.backgroundColor = UIColor.white.withAlphaComponent(0.95)
        containerView.layer.cornerRadius = 30
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 20)
        containerView.layer.shadowRadius = 30
        containerView.layer.shadowOpacity = 0.3
        containerView.translatesAutoresizingMaskIntoConstraints = false

        // 现代化图标和标题
        let iconLabel = UILabel()
        iconLabel.text = "🚀"
        iconLabel.font = UIFont.systemFont(ofSize: 80)
        iconLabel.textAlignment = .center
        iconLabel.translatesAutoresizingMaskIntoConstraints = false

        let titleLabel = UILabel()
        titleLabel.text = "外接屏幕浏览器"
        titleLabel.textColor = UIColor.systemGreen
        titleLabel.font = UIFont.boldSystemFont(ofSize: 56)
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        let subtitleLabel = UILabel()
        subtitleLabel.text = "大屏网页显示"
        subtitleLabel.textColor = UIColor.systemBlue
        subtitleLabel.font = UIFont.systemFont(ofSize: 32, weight: .semibold)
        subtitleLabel.textAlignment = .center
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        // 屏幕信息和状态
        let aspectRatio = screen.bounds.width / screen.bounds.height
        let isMirrored = screen.mirrored != nil
        let infoLabel = UILabel()
        infoLabel.text = """
        ✅ 现代化API正常工作

        📺 屏幕信息:
        分辨率: \(Int(screen.bounds.width)) × \(Int(screen.bounds.height))
        宽高比: \(String(format: "%.1f", aspectRatio)):9
        缩放: \(screen.scale)x

        🎯 使用UIWindowScene (iOS 16+)
        避免所有弃用警告
        现代化架构设计

        ⏰ 时间: \(Date().formatted(date: .omitted, time: .standard))
        """
        infoLabel.textColor = UIColor.label
        infoLabel.font = UIFont.systemFont(ofSize: 24, weight: .medium)
        infoLabel.textAlignment = .center
        infoLabel.numberOfLines = 0
        infoLabel.translatesAutoresizingMaskIntoConstraints = false

        // 状态指示器
        let statusView = UIView()
        statusView.backgroundColor = isMirrored ? UIColor.systemOrange : UIColor.systemGreen
        statusView.layer.cornerRadius = 25
        statusView.translatesAutoresizingMaskIntoConstraints = false

        let statusLabel = UILabel()
        statusLabel.text = isMirrored ? "🟠 镜像模式" : "🟢 扩展模式"
        statusLabel.textColor = UIColor.white
        statusLabel.font = UIFont.boldSystemFont(ofSize: 22)
        statusLabel.textAlignment = .center
        statusLabel.translatesAutoresizingMaskIntoConstraints = false

        // 添加所有视图
        view.addSubview(containerView)
        containerView.addSubview(iconLabel)
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(infoLabel)
        containerView.addSubview(statusView)
        statusView.addSubview(statusLabel)

        // 设置约束
        NSLayoutConstraint.activate([
            // 容器约束
            containerView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            containerView.widthAnchor.constraint(equalTo: view.widthAnchor, multiplier: 0.85),
            containerView.heightAnchor.constraint(equalTo: view.heightAnchor, multiplier: 0.8),

            // 图标约束
            iconLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 40),
            iconLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),

            // 标题约束
            titleLabel.topAnchor.constraint(equalTo: iconLabel.bottomAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),

            // 副标题约束
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 15),
            subtitleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            subtitleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),

            // 信息约束
            infoLabel.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 30),
            infoLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            infoLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),

            // 状态视图约束
            statusView.topAnchor.constraint(equalTo: infoLabel.bottomAnchor, constant: 30),
            statusView.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            statusView.widthAnchor.constraint(equalToConstant: 220),
            statusView.heightAnchor.constraint(equalToConstant: 50),

            // 状态标签约束
            statusLabel.centerXAnchor.constraint(equalTo: statusView.centerXAnchor),
            statusLabel.centerYAnchor.constraint(equalTo: statusView.centerYAnchor)
        ])

        // 添加现代化动画
        addModernAnimations(to: statusView, isMirrored: isMirrored)

        print("🎨 现代化外接屏幕界面已设置")
    }

    private func addModernAnimations(to view: UIView, isMirrored: Bool) {
        if isMirrored {
            // 镜像模式 - 警告脉冲动画
            let pulseAnimation = CABasicAnimation(keyPath: "transform.scale")
            pulseAnimation.duration = 1.0
            pulseAnimation.fromValue = 1.0
            pulseAnimation.toValue = 1.1
            pulseAnimation.autoreverses = true
            pulseAnimation.repeatCount = .infinity
            view.layer.add(pulseAnimation, forKey: "pulse")
        } else {
            // 扩展模式 - 成功呼吸动画
            let breatheAnimation = CABasicAnimation(keyPath: "opacity")
            breatheAnimation.duration = 2.0
            breatheAnimation.fromValue = 0.8
            breatheAnimation.toValue = 1.0
            breatheAnimation.autoreverses = true
            breatheAnimation.repeatCount = .infinity
            view.layer.add(breatheAnimation, forKey: "breathe")
        }
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 更新渐变层大小
        if let gradientLayer = view.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = view.bounds
        }
    }
}

















// MARK: - Browser Main View
struct BrowserMainView: View {
    @ObservedObject var browserViewModel: BrowserViewModel

    var body: some View {
        VStack(spacing: 0) {
            // Address Bar
            AddressBar(browserViewModel: browserViewModel)

            // Web Content
            WebView(browserViewModel: browserViewModel)
        }
        .edgesIgnoringSafeArea(.bottom)
    }
}

// MARK: - Content View
struct ContentView: View {
    @StateObject private var browserViewModel = BrowserViewModel()
    @StateObject private var externalScreenManager: ExternalScreenManager = {
        if #available(iOS 16.0, *) {
            return ExternalScreenManager()
        } else {
            // 为iOS 16以下版本提供兼容性
            fatalError("此应用需要iOS 16.0或更高版本")
        }
    }()

    var body: some View {
        BrowserMainView(browserViewModel: browserViewModel)
            .onAppear {
                // 设置浏览器引用
                externalScreenManager.setBrowserViewModel(browserViewModel)

                // 自动设置外接屏幕浏览器显示
                if externalScreenManager.hasExternalScreen {
                    externalScreenManager.setupExternalBrowserDisplay(with: browserViewModel)
                }
            }
            .onChange(of: externalScreenManager.hasExternalScreen) { oldValue, hasScreen in
                if hasScreen {
                    // 设置浏览器引用
                    externalScreenManager.setBrowserViewModel(browserViewModel)

                    // 外接屏幕连接时自动设置为浏览器显示
                    externalScreenManager.updateExternalContent(.browser)
                    externalScreenManager.setupExternalBrowserDisplay(with: browserViewModel)

                    print("外接屏幕已连接并设置为浏览器显示")
                } else {
                    print("外接屏幕已断开")
                }
            }
    }
}

// MARK: - Modern External Browser Display View Controller
@available(iOS 16.0, *)
class ModernExternalBrowserViewController: UIViewController {
    private var screenInfo: UIScreen?
    private var browserViewModel: BrowserViewModel?
    private var webView: WKWebView!

    func setupForScreen(_ screen: UIScreen, browserViewModel: BrowserViewModel) {
        self.screenInfo = screen
        self.browserViewModel = browserViewModel
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupBrowserInterface()
    }

    private func setupBrowserInterface() {
        guard let screen = screenInfo, let browserVM = browserViewModel else { return }

        view.backgroundColor = UIColor.black

        // 创建WebView
        let webViewConfig = WKWebViewConfiguration()
        webView = WKWebView(frame: view.bounds, configuration: webViewConfig)
        webView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        webView.navigationDelegate = self

        // 设置WebView引用到browserViewModel
        browserVM.webView = webView

        view.addSubview(webView)

        // 加载初始URL
        if let url = URL(string: browserVM.currentURL) {
            webView.load(URLRequest(url: url))
        }

        print("✅ 外接屏幕浏览器界面已设置")
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        webView?.frame = view.bounds
    }
}

// MARK: - WKNavigationDelegate for External Browser
@available(iOS 16.0, *)
extension ModernExternalBrowserViewController: WKNavigationDelegate {
    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        browserViewModel?.updateNavigationState()
    }

    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        browserViewModel?.updateNavigationState()
        if let url = webView.url {
            browserViewModel?.currentURL = url.absoluteString
        }
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        browserViewModel?.updateNavigationState()
        print("❌ 外接屏幕浏览器加载失败: \(error.localizedDescription)")
    }
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
    }
}
