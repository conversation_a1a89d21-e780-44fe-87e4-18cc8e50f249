# 浏览器URL输入测试用例

## 测试用例列表

### 1. 完整URL测试
- `https://www.apple.com` - 应该直接加载Apple官网
- `http://example.com` - 应该加载HTTP网站
- `https://github.com` - 应该加载GitHub

### 2. 域名自动补全测试
- `apple.com` - 应该自动添加https://前缀
- `github.com` - 应该自动添加https://前缀
- `baidu.com` - 应该自动添加https://前缀

### 3. 搜索功能测试
- `Swift编程` - 应该使用Google搜索
- `iOS开发教程` - 应该使用Google搜索
- `hello world` - 应该使用Google搜索

### 4. 特殊情况测试
- 空输入 - 应该保持当前URL
- 只有空格 - 应该保持当前URL
- `localhost:3000` - 应该自动添加https://前缀

## 预期行为

1. **智能URL处理**：
   - 如果输入包含协议（http://或https://），直接使用
   - 如果输入看起来像域名（包含.且无空格），自动添加https://
   - 其他情况作为搜索词处理

2. **用户体验**：
   - 点击地址栏时自动选中当前URL
   - 显示简化的URL（去掉协议前缀）
   - 支持取消编辑功能
   - 实时显示加载进度

3. **错误处理**：
   - 无效URL应该有适当的错误提示
   - 网络错误应该显示错误页面
